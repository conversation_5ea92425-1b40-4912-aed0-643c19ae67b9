package com.store.app.petstore.Controllers.Admin.Statistic;

import com.store.app.petstore.DAO.StatisticDAO.BusinessStatisticsDAO;
import com.store.app.petstore.DAO.StatisticDAO.InventoryStatisticsDAO;
import com.store.app.petstore.DAO.StatisticDAO.CustomerAnalyticsDAO;
import com.store.app.petstore.Models.Records.BusinessStatistics;
import com.store.app.petstore.Models.Records.InventoryStatistics;
import com.store.app.petstore.Models.Records.CustomerAnalytics;

import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.chart.*;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;

import java.net.URL;
import java.sql.SQLException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * Comprehensive dashboard controller for pet shop business analytics
 * Provides a unified view of all key business metrics and insights
 */
public class ComprehensiveDashboardController implements Initializable {
    
    // === Quick Statistics Labels ===
    @FXML private Label totalRevenueLabel;
    @FXML private Label totalProfitLabel;
    @FXML private Label totalOrdersLabel;
    @FXML private Label averageOrderValueLabel;
    @FXML private Label revenueGrowthLabel;
    @FXML private Label customerCountLabel;
    @FXML private Label newCustomersLabel;
    @FXML private Label inventoryValueLabel;
    @FXML private Label lowStockAlertsLabel;
    
    // === Charts ===
    @FXML private LineChart<String, Number> revenueChart;
    @FXML private PieChart salesDistributionChart;
    @FXML private BarChart<String, Number> customerSegmentChart;
    @FXML private AreaChart<String, Number> inventoryTrendChart;
    
    // === Tables ===
    @FXML private TableView<InventoryStatistics> lowStockTable;
    @FXML private TableView<CustomerAnalytics> topCustomersTable;
    @FXML private TableView<InventoryStatistics> fastMovingItemsTable;
    
    // === Controls ===
    @FXML private DatePicker startDatePicker;
    @FXML private DatePicker endDatePicker;
    @FXML private ChoiceBox<String> periodChoiceBox;
    @FXML private Button refreshButton;
    @FXML private ProgressBar loadingProgressBar;
    @FXML private Label statusLabel;
    
    // === Tab Pane for Different Views ===
    @FXML private TabPane mainTabPane;
    @FXML private Tab overviewTab;
    @FXML private Tab salesTab;
    @FXML private Tab customersTab;
    @FXML private Tab inventoryTab;
    
    private BusinessStatistics currentStats;
    
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupControls();
        setupTables();
        setupCharts();
        loadDefaultData();
    }
    
    /**
     * Setup initial control values and event handlers
     */
    private void setupControls() {
        // Set default date range (last 30 days)
        endDatePicker.setValue(LocalDate.now());
        startDatePicker.setValue(LocalDate.now().minusDays(30));
        
        // Setup period choice box
        periodChoiceBox.setItems(FXCollections.observableArrayList(
            "Daily", "Weekly", "Monthly", "Yearly"
        ));
        periodChoiceBox.setValue("Daily");
        
        // Setup refresh button
        refreshButton.setOnAction(e -> refreshDashboard());
        
        // Hide loading indicator initially
        loadingProgressBar.setVisible(false);
        statusLabel.setText("Ready");
    }
    
    /**
     * Setup table columns and properties
     */
    private void setupTables() {
        // Low Stock Table
        setupLowStockTable();
        
        // Top Customers Table
        setupTopCustomersTable();
        
        // Fast Moving Items Table
        setupFastMovingItemsTable();
    }
    
    private void setupLowStockTable() {
        TableColumn<InventoryStatistics, String> nameCol = new TableColumn<>("Item Name");
        nameCol.setCellValueFactory(new PropertyValueFactory<>("itemName"));
        nameCol.setPrefWidth(150);
        
        TableColumn<InventoryStatistics, String> typeCol = new TableColumn<>("Type");
        typeCol.setCellValueFactory(new PropertyValueFactory<>("itemType"));
        typeCol.setPrefWidth(80);
        
        TableColumn<InventoryStatistics, Integer> stockCol = new TableColumn<>("Current Stock");
        stockCol.setCellValueFactory(new PropertyValueFactory<>("currentStock"));
        stockCol.setPrefWidth(100);
        
        TableColumn<InventoryStatistics, Integer> minCol = new TableColumn<>("Min Stock");
        minCol.setCellValueFactory(new PropertyValueFactory<>("minimumStock"));
        minCol.setPrefWidth(80);
        
        TableColumn<InventoryStatistics, String> statusCol = new TableColumn<>("Status");
        statusCol.setCellValueFactory(new PropertyValueFactory<>("stockStatusDisplay"));
        statusCol.setPrefWidth(100);
        
        lowStockTable.getColumns().addAll(nameCol, typeCol, stockCol, minCol, statusCol);
    }
    
    private void setupTopCustomersTable() {
        TableColumn<CustomerAnalytics, String> nameCol = new TableColumn<>("Customer Name");
        nameCol.setCellValueFactory(new PropertyValueFactory<>("customerName"));
        nameCol.setPrefWidth(150);
        
        TableColumn<CustomerAnalytics, Integer> ordersCol = new TableColumn<>("Orders");
        ordersCol.setCellValueFactory(new PropertyValueFactory<>("totalOrders"));
        ordersCol.setPrefWidth(80);
        
        TableColumn<CustomerAnalytics, String> spentCol = new TableColumn<>("Total Spent");
        spentCol.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getFormattedTotalSpent()));
        spentCol.setPrefWidth(100);
        
        TableColumn<CustomerAnalytics, String> segmentCol = new TableColumn<>("Segment");
        segmentCol.setCellValueFactory(new PropertyValueFactory<>("customerSegmentDisplay"));
        segmentCol.setPrefWidth(100);
        
        topCustomersTable.getColumns().addAll(nameCol, ordersCol, spentCol, segmentCol);
    }
    
    private void setupFastMovingItemsTable() {
        TableColumn<InventoryStatistics, String> nameCol = new TableColumn<>("Item Name");
        nameCol.setCellValueFactory(new PropertyValueFactory<>("itemName"));
        nameCol.setPrefWidth(150);
        
        TableColumn<InventoryStatistics, String> categoryCol = new TableColumn<>("Category");
        categoryCol.setCellValueFactory(new PropertyValueFactory<>("category"));
        categoryCol.setPrefWidth(100);
        
        TableColumn<InventoryStatistics, Integer> soldCol = new TableColumn<>("Sold This Month");
        soldCol.setCellValueFactory(new PropertyValueFactory<>("soldThisMonth"));
        soldCol.setPrefWidth(120);
        
        TableColumn<InventoryStatistics, String> turnoverCol = new TableColumn<>("Turnover Rate");
        turnoverCol.setCellValueFactory(cellData -> 
            new javafx.beans.property.SimpleStringProperty(cellData.getValue().getFormattedTurnoverRate()));
        turnoverCol.setPrefWidth(100);
        
        fastMovingItemsTable.getColumns().addAll(nameCol, categoryCol, soldCol, turnoverCol);
    }
    
    /**
     * Setup chart properties
     */
    private void setupCharts() {
        // Revenue Chart
        revenueChart.setTitle("Revenue Trend");
        revenueChart.setCreateSymbols(false);
        
        // Sales Distribution Chart
        salesDistributionChart.setTitle("Sales Distribution (Pets vs Products)");
        
        // Customer Segment Chart
        customerSegmentChart.setTitle("Customer Segments");
        
        // Inventory Trend Chart
        inventoryTrendChart.setTitle("Inventory Value Trend");
    }
    
    /**
     * Load default dashboard data
     */
    private void loadDefaultData() {
        refreshDashboard();
    }
    
    /**
     * Refresh all dashboard data
     */
    @FXML
    private void refreshDashboard() {
        LocalDate startDate = startDatePicker.getValue();
        LocalDate endDate = endDatePicker.getValue();
        String periodType = periodChoiceBox.getValue().toLowerCase();
        
        if (startDate == null || endDate == null) {
            showAlert("Please select both start and end dates.");
            return;
        }
        
        if (startDate.isAfter(endDate)) {
            showAlert("Start date must be before end date.");
            return;
        }
        
        loadingProgressBar.setVisible(true);
        statusLabel.setText("Loading dashboard data...");
        refreshButton.setDisable(true);
        
        Task<Void> loadTask = new Task<Void>() {
            @Override
            protected Void call() throws Exception {
                // Load business statistics
                currentStats = BusinessStatisticsDAO.getBusinessStatistics(startDate, endDate, periodType);
                
                // Load other data
                List<InventoryStatistics> lowStockItems = InventoryStatisticsDAO.getLowStockItems();
                List<CustomerAnalytics> topCustomers = CustomerAnalyticsDAO.getTopCustomersBySpending(10);
                List<InventoryStatistics> fastMovingItems = InventoryStatisticsDAO.getFastMovingItems(10);
                
                Platform.runLater(() -> {
                    updateQuickStatistics();
                    updateCharts();
                    updateTables(lowStockItems, topCustomers, fastMovingItems);
                });
                
                return null;
            }
            
            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    loadingProgressBar.setVisible(false);
                    statusLabel.setText("Dashboard updated successfully");
                    refreshButton.setDisable(false);
                });
            }
            
            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    loadingProgressBar.setVisible(false);
                    statusLabel.setText("Error loading dashboard data");
                    refreshButton.setDisable(false);
                    showAlert("Error loading dashboard data: " + getException().getMessage());
                });
            }
        };
        
        new Thread(loadTask).start();
    }
    
    /**
     * Update quick statistics labels
     */
    private void updateQuickStatistics() {
        if (currentStats == null) return;
        
        totalRevenueLabel.setText(currentStats.getFormattedRevenue());
        totalProfitLabel.setText(currentStats.getFormattedProfit());
        totalOrdersLabel.setText(String.valueOf(currentStats.getTotalOrders()));
        averageOrderValueLabel.setText(currentStats.getFormattedAverageOrderValue());
        revenueGrowthLabel.setText(currentStats.getFormattedRevenueGrowth());
        customerCountLabel.setText(String.valueOf(currentStats.getTotalCustomers()));
        newCustomersLabel.setText(String.valueOf(currentStats.getNewCustomers()));
        
        // Set growth label color based on positive/negative growth
        if (currentStats.getRevenueGrowth() >= 0) {
            revenueGrowthLabel.setStyle("-fx-text-fill: #28a745;"); // Green
        } else {
            revenueGrowthLabel.setStyle("-fx-text-fill: #dc3545;"); // Red
        }
        
        try {
            double inventoryValue = InventoryStatisticsDAO.getTotalInventoryValue();
            inventoryValueLabel.setText(String.format("%,.2f", inventoryValue));
            
            int lowStockCount = InventoryStatisticsDAO.getLowStockItems().size();
            lowStockAlertsLabel.setText(String.valueOf(lowStockCount));
            
            // Set low stock alert color
            if (lowStockCount > 0) {
                lowStockAlertsLabel.setStyle("-fx-text-fill: #dc3545;"); // Red
            } else {
                lowStockAlertsLabel.setStyle("-fx-text-fill: #28a745;"); // Green
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Update all charts with current data
     */
    private void updateCharts() {
        updateRevenueChart();
        updateSalesDistributionChart();
        updateCustomerSegmentChart();
    }
    
    private void updateRevenueChart() {
        try {
            Map<String, Double> dailyRevenue = BusinessStatisticsDAO.getDailyRevenue(
                startDatePicker.getValue(), endDatePicker.getValue());
            
            XYChart.Series<String, Number> series = new XYChart.Series<>();
            series.setName("Daily Revenue");
            
            for (Map.Entry<String, Double> entry : dailyRevenue.entrySet()) {
                series.getData().add(new XYChart.Data<>(entry.getKey(), entry.getValue()));
            }
            
            revenueChart.getData().clear();
            revenueChart.getData().add(series);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    
    private void updateSalesDistributionChart() {
        if (currentStats == null) return;
        
        ObservableList<PieChart.Data> pieChartData = FXCollections.observableArrayList(
            new PieChart.Data("Pets Sold", currentStats.getPetsSold()),
            new PieChart.Data("Products Sold", currentStats.getProductsSold())
        );
        
        salesDistributionChart.setData(pieChartData);
    }
    
    private void updateCustomerSegmentChart() {
        try {
            Map<String, Integer> segmentDistribution = CustomerAnalyticsDAO.getCustomerSegmentDistribution();
            
            XYChart.Series<String, Number> series = new XYChart.Series<>();
            series.setName("Customer Count");
            
            for (Map.Entry<String, Integer> entry : segmentDistribution.entrySet()) {
                series.getData().add(new XYChart.Data<>(
                    entry.getKey().substring(0, 1).toUpperCase() + entry.getKey().substring(1), 
                    entry.getValue()));
            }
            
            customerSegmentChart.getData().clear();
            customerSegmentChart.getData().add(series);
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    
    /**
     * Update all tables with current data
     */
    private void updateTables(List<InventoryStatistics> lowStockItems, 
                            List<CustomerAnalytics> topCustomers,
                            List<InventoryStatistics> fastMovingItems) {
        lowStockTable.setItems(FXCollections.observableArrayList(lowStockItems));
        topCustomersTable.setItems(FXCollections.observableArrayList(topCustomers));
        fastMovingItemsTable.setItems(FXCollections.observableArrayList(fastMovingItems));
    }
    
    /**
     * Show alert dialog
     */
    private void showAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("Dashboard Warning");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    /**
     * Export dashboard data (placeholder for future implementation)
     */
    @FXML
    private void exportDashboard() {
        // TODO: Implement export functionality
        showAlert("Export functionality will be implemented in future version.");
    }
}
