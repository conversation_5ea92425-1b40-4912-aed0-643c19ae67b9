package com.store.app.petstore.Models.Records;

import java.time.LocalDate;

/**
 * Enhanced model for best-selling items with additional business metrics
 */
public class BestSellingItem {
    private String name;
    private String category;
    private int quantitySold;
    private double totalRevenue;
    private double averagePrice;
    private LocalDate saleDate;
    private String itemType; // "pet" or "product"

    public BestSellingItem(String name, String category, int quantitySold,
                          double totalRevenue, LocalDate saleDate, String itemType) {
        this.name = name;
        this.category = category;
        this.quantitySold = quantitySold;
        this.totalRevenue = totalRevenue;
        this.averagePrice = quantitySold > 0 ? totalRevenue / quantitySold : 0;
        this.saleDate = saleDate;
        this.itemType = itemType;
    }

    // Legacy constructor for backward compatibility
    public BestSellingItem(String name, int quantitySold, LocalDate saleDate) {
        this.name = name;
        this.quantitySold = quantitySold;
        this.saleDate = saleDate;
        this.itemType = "unknown";
        this.category = "unknown";
        this.totalRevenue = 0;
        this.averagePrice = 0;
    }

    // Getters
    public String getName() { return name; }
    public String getCategory() { return category; }
    public int getQuantitySold() { return quantitySold; }
    public double getTotalRevenue() { return totalRevenue; }
    public double getAveragePrice() { return averagePrice; }
    public LocalDate getSaleDate() { return saleDate; }
    public String getItemType() { return itemType; }

    // Setters
    public void setName(String name) { this.name = name; }
    public void setCategory(String category) { this.category = category; }
    public void setQuantitySold(int quantitySold) {
        this.quantitySold = quantitySold;
        this.averagePrice = quantitySold > 0 ? totalRevenue / quantitySold : 0;
    }
    public void setTotalRevenue(double totalRevenue) {
        this.totalRevenue = totalRevenue;
        this.averagePrice = quantitySold > 0 ? totalRevenue / quantitySold : 0;
    }
    public void setSaleDate(LocalDate saleDate) { this.saleDate = saleDate; }
    public void setItemType(String itemType) { this.itemType = itemType; }
}
