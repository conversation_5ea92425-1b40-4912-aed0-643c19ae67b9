package com.store.app.petstore.Models.Records;

import java.time.LocalDate;

/**
 * Comprehensive business statistics model for pet shop dashboard
 * Contains key performance indicators and business metrics
 */
public class BusinessStatistics {
    
    // Financial Metrics
    private double totalRevenue;
    private double totalProfit;
    private double averageOrderValue;
    private double profitMargin;
    private double revenueGrowth; // Percentage growth compared to previous period
    
    // Sales Metrics
    private int totalOrders;
    private int totalItemsSold;
    private int petsSold;
    private int productsSold;
    private int ordersGrowth; // Growth compared to previous period
    
    // Customer Metrics
    private int totalCustomers;
    private int newCustomers;
    private int returningCustomers;
    private double customerRetentionRate;
    
    // Inventory Metrics
    private int totalPetsInStock;
    private int totalProductsInStock;
    private int lowStockProducts; // Products below minimum threshold
    private double inventoryTurnover;
    
    // Time Period
    private LocalDate startDate;
    private LocalDate endDate;
    private String periodType; // "daily", "weekly", "monthly", "yearly"
    
    // Constructors
    public BusinessStatistics() {}
    
    public BusinessStatistics(LocalDate startDate, LocalDate endDate, String periodType) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.periodType = periodType;
    }
    
    // Financial Metrics Getters/Setters
    public double getTotalRevenue() { return totalRevenue; }
    public void setTotalRevenue(double totalRevenue) { 
        this.totalRevenue = totalRevenue;
        calculateProfitMargin();
    }
    
    public double getTotalProfit() { return totalProfit; }
    public void setTotalProfit(double totalProfit) { 
        this.totalProfit = totalProfit;
        calculateProfitMargin();
    }
    
    public double getAverageOrderValue() { return averageOrderValue; }
    public void setAverageOrderValue(double averageOrderValue) { this.averageOrderValue = averageOrderValue; }
    
    public double getProfitMargin() { return profitMargin; }
    public void setProfitMargin(double profitMargin) { this.profitMargin = profitMargin; }
    
    public double getRevenueGrowth() { return revenueGrowth; }
    public void setRevenueGrowth(double revenueGrowth) { this.revenueGrowth = revenueGrowth; }
    
    // Sales Metrics Getters/Setters
    public int getTotalOrders() { return totalOrders; }
    public void setTotalOrders(int totalOrders) { 
        this.totalOrders = totalOrders;
        calculateAverageOrderValue();
    }
    
    public int getTotalItemsSold() { return totalItemsSold; }
    public void setTotalItemsSold(int totalItemsSold) { this.totalItemsSold = totalItemsSold; }
    
    public int getPetsSold() { return petsSold; }
    public void setPetsSold(int petsSold) { this.petsSold = petsSold; }
    
    public int getProductsSold() { return productsSold; }
    public void setProductsSold(int productsSold) { this.productsSold = productsSold; }
    
    public int getOrdersGrowth() { return ordersGrowth; }
    public void setOrdersGrowth(int ordersGrowth) { this.ordersGrowth = ordersGrowth; }
    
    // Customer Metrics Getters/Setters
    public int getTotalCustomers() { return totalCustomers; }
    public void setTotalCustomers(int totalCustomers) { this.totalCustomers = totalCustomers; }
    
    public int getNewCustomers() { return newCustomers; }
    public void setNewCustomers(int newCustomers) { 
        this.newCustomers = newCustomers;
        calculateCustomerRetentionRate();
    }
    
    public int getReturningCustomers() { return returningCustomers; }
    public void setReturningCustomers(int returningCustomers) { 
        this.returningCustomers = returningCustomers;
        calculateCustomerRetentionRate();
    }
    
    public double getCustomerRetentionRate() { return customerRetentionRate; }
    public void setCustomerRetentionRate(double customerRetentionRate) { this.customerRetentionRate = customerRetentionRate; }
    
    // Inventory Metrics Getters/Setters
    public int getTotalPetsInStock() { return totalPetsInStock; }
    public void setTotalPetsInStock(int totalPetsInStock) { this.totalPetsInStock = totalPetsInStock; }
    
    public int getTotalProductsInStock() { return totalProductsInStock; }
    public void setTotalProductsInStock(int totalProductsInStock) { this.totalProductsInStock = totalProductsInStock; }
    
    public int getLowStockProducts() { return lowStockProducts; }
    public void setLowStockProducts(int lowStockProducts) { this.lowStockProducts = lowStockProducts; }
    
    public double getInventoryTurnover() { return inventoryTurnover; }
    public void setInventoryTurnover(double inventoryTurnover) { this.inventoryTurnover = inventoryTurnover; }
    
    // Time Period Getters/Setters
    public LocalDate getStartDate() { return startDate; }
    public void setStartDate(LocalDate startDate) { this.startDate = startDate; }
    
    public LocalDate getEndDate() { return endDate; }
    public void setEndDate(LocalDate endDate) { this.endDate = endDate; }
    
    public String getPeriodType() { return periodType; }
    public void setPeriodType(String periodType) { this.periodType = periodType; }
    
    // Helper Methods for Automatic Calculations
    private void calculateProfitMargin() {
        if (totalRevenue > 0) {
            this.profitMargin = (totalProfit / totalRevenue) * 100;
        } else {
            this.profitMargin = 0;
        }
    }
    
    private void calculateAverageOrderValue() {
        if (totalOrders > 0) {
            this.averageOrderValue = totalRevenue / totalOrders;
        } else {
            this.averageOrderValue = 0;
        }
    }
    
    private void calculateCustomerRetentionRate() {
        if (totalCustomers > 0) {
            this.customerRetentionRate = ((double) returningCustomers / totalCustomers) * 100;
        } else {
            this.customerRetentionRate = 0;
        }
    }
    
    // Utility Methods
    public String getFormattedRevenue() {
        return String.format("%,.2f", totalRevenue);
    }
    
    public String getFormattedProfit() {
        return String.format("%,.2f", totalProfit);
    }
    
    public String getFormattedProfitMargin() {
        return String.format("%.1f%%", profitMargin);
    }
    
    public String getFormattedRevenueGrowth() {
        return String.format("%+.1f%%", revenueGrowth);
    }
    
    public String getFormattedAverageOrderValue() {
        return String.format("%,.2f", averageOrderValue);
    }
    
    public String getFormattedCustomerRetentionRate() {
        return String.format("%.1f%%", customerRetentionRate);
    }
}
