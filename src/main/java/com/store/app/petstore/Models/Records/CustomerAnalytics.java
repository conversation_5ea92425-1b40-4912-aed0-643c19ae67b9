package com.store.app.petstore.Models.Records;

import java.time.LocalDate;

/**
 * Customer analytics model for tracking customer behavior and value
 */
public class CustomerAnalytics {
    
    // Customer Information
    private int customerId;
    private String customerName;
    private String phone;
    private LocalDate firstPurchaseDate;
    private LocalDate lastPurchaseDate;
    
    // Purchase Behavior
    private int totalOrders;
    private double totalSpent;
    private double averageOrderValue;
    private int daysSinceLastPurchase;
    private String customerSegment; // "new", "regular", "vip", "at_risk", "lost"
    
    // Product Preferences
    private int petsPurchased;
    private int productsPurchased;
    private String favoriteCategory;
    private double petSpending;
    private double productSpending;
    
    // Loyalty Metrics
    private double customerLifetimeValue;
    private int purchaseFrequency; // Days between purchases
    private boolean isReturningCustomer;
    private double loyaltyScore; // 0-100 based on various factors
    
    // Constructors
    public CustomerAnalytics() {}
    
    public CustomerAnalytics(int customerId, String customerName, String phone) {
        this.customerId = customerId;
        this.customerName = customerName;
        this.phone = phone;
    }
    
    // Getters and Setters
    public int getCustomerId() { return customerId; }
    public void setCustomerId(int customerId) { this.customerId = customerId; }
    
    public String getCustomerName() { return customerName; }
    public void setCustomerName(String customerName) { this.customerName = customerName; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public LocalDate getFirstPurchaseDate() { return firstPurchaseDate; }
    public void setFirstPurchaseDate(LocalDate firstPurchaseDate) { this.firstPurchaseDate = firstPurchaseDate; }
    
    public LocalDate getLastPurchaseDate() { return lastPurchaseDate; }
    public void setLastPurchaseDate(LocalDate lastPurchaseDate) { 
        this.lastPurchaseDate = lastPurchaseDate;
        calculateDaysSinceLastPurchase();
        updateCustomerSegment();
    }
    
    public int getTotalOrders() { return totalOrders; }
    public void setTotalOrders(int totalOrders) { 
        this.totalOrders = totalOrders;
        calculateAverageOrderValue();
        calculatePurchaseFrequency();
        updateCustomerSegment();
    }
    
    public double getTotalSpent() { return totalSpent; }
    public void setTotalSpent(double totalSpent) { 
        this.totalSpent = totalSpent;
        calculateAverageOrderValue();
        this.customerLifetimeValue = totalSpent; // Simplified CLV calculation
        updateCustomerSegment();
    }
    
    public double getAverageOrderValue() { return averageOrderValue; }
    public void setAverageOrderValue(double averageOrderValue) { this.averageOrderValue = averageOrderValue; }
    
    public int getDaysSinceLastPurchase() { return daysSinceLastPurchase; }
    public void setDaysSinceLastPurchase(int daysSinceLastPurchase) { this.daysSinceLastPurchase = daysSinceLastPurchase; }
    
    public String getCustomerSegment() { return customerSegment; }
    public void setCustomerSegment(String customerSegment) { this.customerSegment = customerSegment; }
    
    public int getPetsPurchased() { return petsPurchased; }
    public void setPetsPurchased(int petsPurchased) { this.petsPurchased = petsPurchased; }
    
    public int getProductsPurchased() { return productsPurchased; }
    public void setProductsPurchased(int productsPurchased) { this.productsPurchased = productsPurchased; }
    
    public String getFavoriteCategory() { return favoriteCategory; }
    public void setFavoriteCategory(String favoriteCategory) { this.favoriteCategory = favoriteCategory; }
    
    public double getPetSpending() { return petSpending; }
    public void setPetSpending(double petSpending) { this.petSpending = petSpending; }
    
    public double getProductSpending() { return productSpending; }
    public void setProductSpending(double productSpending) { this.productSpending = productSpending; }
    
    public double getCustomerLifetimeValue() { return customerLifetimeValue; }
    public void setCustomerLifetimeValue(double customerLifetimeValue) { this.customerLifetimeValue = customerLifetimeValue; }
    
    public int getPurchaseFrequency() { return purchaseFrequency; }
    public void setPurchaseFrequency(int purchaseFrequency) { this.purchaseFrequency = purchaseFrequency; }
    
    public boolean isReturningCustomer() { return isReturningCustomer; }
    public void setReturningCustomer(boolean returningCustomer) { this.isReturningCustomer = returningCustomer; }
    
    public double getLoyaltyScore() { return loyaltyScore; }
    public void setLoyaltyScore(double loyaltyScore) { this.loyaltyScore = loyaltyScore; }
    
    // Business Logic Methods
    private void calculateAverageOrderValue() {
        if (totalOrders > 0) {
            this.averageOrderValue = totalSpent / totalOrders;
        } else {
            this.averageOrderValue = 0;
        }
    }
    
    private void calculateDaysSinceLastPurchase() {
        if (lastPurchaseDate != null) {
            this.daysSinceLastPurchase = (int) java.time.temporal.ChronoUnit.DAYS.between(lastPurchaseDate, LocalDate.now());
        } else {
            this.daysSinceLastPurchase = Integer.MAX_VALUE;
        }
    }
    
    private void calculatePurchaseFrequency() {
        if (firstPurchaseDate != null && lastPurchaseDate != null && totalOrders > 1) {
            long totalDays = java.time.temporal.ChronoUnit.DAYS.between(firstPurchaseDate, lastPurchaseDate);
            this.purchaseFrequency = (int) (totalDays / (totalOrders - 1));
        } else {
            this.purchaseFrequency = 0;
        }
    }
    
    private void updateCustomerSegment() {
        this.isReturningCustomer = totalOrders > 1;
        
        if (totalOrders == 0) {
            this.customerSegment = "prospect";
        } else if (totalOrders == 1) {
            this.customerSegment = "new";
        } else if (daysSinceLastPurchase > 180) {
            this.customerSegment = "lost";
        } else if (daysSinceLastPurchase > 90) {
            this.customerSegment = "at_risk";
        } else if (totalSpent > 1000 || totalOrders > 10) {
            this.customerSegment = "vip";
        } else {
            this.customerSegment = "regular";
        }
        
        calculateLoyaltyScore();
    }
    
    private void calculateLoyaltyScore() {
        double score = 0;
        
        // Order frequency (0-30 points)
        if (totalOrders >= 10) score += 30;
        else if (totalOrders >= 5) score += 20;
        else if (totalOrders >= 2) score += 10;
        
        // Spending amount (0-30 points)
        if (totalSpent >= 1000) score += 30;
        else if (totalSpent >= 500) score += 20;
        else if (totalSpent >= 100) score += 10;
        
        // Recency (0-25 points)
        if (daysSinceLastPurchase <= 30) score += 25;
        else if (daysSinceLastPurchase <= 60) score += 15;
        else if (daysSinceLastPurchase <= 90) score += 5;
        
        // Purchase diversity (0-15 points)
        if (petsPurchased > 0 && productsPurchased > 0) score += 15;
        else if (petsPurchased > 0 || productsPurchased > 0) score += 10;
        
        this.loyaltyScore = Math.min(100, score);
    }
    
    // Utility Methods
    public String getFormattedTotalSpent() {
        return String.format("%,.2f", totalSpent);
    }
    
    public String getFormattedAverageOrderValue() {
        return String.format("%,.2f", averageOrderValue);
    }
    
    public String getFormattedCustomerLifetimeValue() {
        return String.format("%,.2f", customerLifetimeValue);
    }
    
    public String getCustomerSegmentDisplay() {
        switch (customerSegment) {
            case "new": return "New Customer";
            case "regular": return "Regular Customer";
            case "vip": return "VIP Customer";
            case "at_risk": return "At Risk";
            case "lost": return "Lost Customer";
            case "prospect": return "Prospect";
            default: return "Unknown";
        }
    }
    
    public String getSegmentColor() {
        switch (customerSegment) {
            case "vip": return "#28a745"; // Green
            case "regular": return "#17a2b8"; // Blue
            case "new": return "#ffc107"; // Yellow
            case "at_risk": return "#fd7e14"; // Orange
            case "lost": return "#dc3545"; // Red
            default: return "#6c757d"; // Gray
        }
    }
}
