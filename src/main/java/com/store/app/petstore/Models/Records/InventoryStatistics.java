package com.store.app.petstore.Models.Records;

/**
 * Inventory statistics model for tracking stock levels and inventory health
 */
public class InventoryStatistics {
    
    // Product Inventory
    private String itemName;
    private String itemType; // "pet" or "product"
    private String category;
    private int currentStock;
    private int minimumStock;
    private int soldThisMonth;
    private double turnoverRate;
    private String stockStatus; // "healthy", "low", "out_of_stock", "overstocked"
    
    // Financial Impact
    private double unitPrice;
    private double totalValue; // currentStock * unitPrice
    private double monthlyRevenue; // soldThisMonth * unitPrice
    
    // Constructors
    public InventoryStatistics() {}
    
    public InventoryStatistics(String itemName, String itemType, String category, 
                             int currentStock, int minimumStock, int soldThisMonth, 
                             double unitPrice) {
        this.itemName = itemName;
        this.itemType = itemType;
        this.category = category;
        this.currentStock = currentStock;
        this.minimumStock = minimumStock;
        this.soldThisMonth = soldThisMonth;
        this.unitPrice = unitPrice;
        
        // Calculate derived values
        this.totalValue = currentStock * unitPrice;
        this.monthlyRevenue = soldThisMonth * unitPrice;
        this.turnoverRate = calculateTurnoverRate();
        this.stockStatus = determineStockStatus();
    }
    
    // Getters and Setters
    public String getItemName() { return itemName; }
    public void setItemName(String itemName) { this.itemName = itemName; }
    
    public String getItemType() { return itemType; }
    public void setItemType(String itemType) { this.itemType = itemType; }
    
    public String getCategory() { return category; }
    public void setCategory(String category) { this.category = category; }
    
    public int getCurrentStock() { return currentStock; }
    public void setCurrentStock(int currentStock) { 
        this.currentStock = currentStock;
        this.totalValue = currentStock * unitPrice;
        this.stockStatus = determineStockStatus();
    }
    
    public int getMinimumStock() { return minimumStock; }
    public void setMinimumStock(int minimumStock) { 
        this.minimumStock = minimumStock;
        this.stockStatus = determineStockStatus();
    }
    
    public int getSoldThisMonth() { return soldThisMonth; }
    public void setSoldThisMonth(int soldThisMonth) { 
        this.soldThisMonth = soldThisMonth;
        this.monthlyRevenue = soldThisMonth * unitPrice;
        this.turnoverRate = calculateTurnoverRate();
    }
    
    public double getTurnoverRate() { return turnoverRate; }
    public void setTurnoverRate(double turnoverRate) { this.turnoverRate = turnoverRate; }
    
    public String getStockStatus() { return stockStatus; }
    public void setStockStatus(String stockStatus) { this.stockStatus = stockStatus; }
    
    public double getUnitPrice() { return unitPrice; }
    public void setUnitPrice(double unitPrice) { 
        this.unitPrice = unitPrice;
        this.totalValue = currentStock * unitPrice;
        this.monthlyRevenue = soldThisMonth * unitPrice;
    }
    
    public double getTotalValue() { return totalValue; }
    public void setTotalValue(double totalValue) { this.totalValue = totalValue; }
    
    public double getMonthlyRevenue() { return monthlyRevenue; }
    public void setMonthlyRevenue(double monthlyRevenue) { this.monthlyRevenue = monthlyRevenue; }
    
    // Business Logic Methods
    private double calculateTurnoverRate() {
        if (currentStock > 0) {
            return (double) soldThisMonth / currentStock;
        }
        return 0.0;
    }
    
    private String determineStockStatus() {
        if (currentStock == 0) {
            return "out_of_stock";
        } else if (currentStock <= minimumStock) {
            return "low";
        } else if (currentStock > minimumStock * 3) {
            return "overstocked";
        } else {
            return "healthy";
        }
    }
    
    public boolean isLowStock() {
        return "low".equals(stockStatus) || "out_of_stock".equals(stockStatus);
    }
    
    public boolean isOutOfStock() {
        return "out_of_stock".equals(stockStatus);
    }
    
    public boolean isOverstocked() {
        return "overstocked".equals(stockStatus);
    }
    
    public int getStockNeeded() {
        if (currentStock < minimumStock) {
            return minimumStock - currentStock;
        }
        return 0;
    }
    
    // Formatted Display Methods
    public String getFormattedTotalValue() {
        return String.format("%,.2f", totalValue);
    }
    
    public String getFormattedMonthlyRevenue() {
        return String.format("%,.2f", monthlyRevenue);
    }
    
    public String getFormattedUnitPrice() {
        return String.format("%,.2f", unitPrice);
    }
    
    public String getFormattedTurnoverRate() {
        return String.format("%.2f", turnoverRate);
    }
    
    public String getStockStatusDisplay() {
        switch (stockStatus) {
            case "healthy": return "Healthy";
            case "low": return "Low Stock";
            case "out_of_stock": return "Out of Stock";
            case "overstocked": return "Overstocked";
            default: return "Unknown";
        }
    }
    
    public String getStockStatusColor() {
        switch (stockStatus) {
            case "healthy": return "#28a745"; // Green
            case "low": return "#ffc107"; // Yellow
            case "out_of_stock": return "#dc3545"; // Red
            case "overstocked": return "#17a2b8"; // Blue
            default: return "#6c757d"; // Gray
        }
    }
}
