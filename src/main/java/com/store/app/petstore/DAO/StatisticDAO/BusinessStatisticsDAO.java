package com.store.app.petstore.DAO.StatisticDAO;

import com.store.app.petstore.DAO.DatabaseUtil;
import com.store.app.petstore.Models.Records.BusinessStatistics;

import java.sql.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Enhanced DAO for comprehensive business statistics
 * Provides accurate and meaningful business metrics for pet shop management
 */
public class BusinessStatisticsDAO {
    
    /**
     * Get comprehensive business statistics for a specific period
     */
    public static BusinessStatistics getBusinessStatistics(LocalDate startDate, LocalDate endDate, String periodType) throws SQLException {
        BusinessStatistics stats = new BusinessStatistics(startDate, endDate, periodType);
        Connection connection = DatabaseUtil.getConnection();
        
        try {
            // Get financial metrics
            setFinancialMetrics(connection, stats, startDate, endDate);
            
            // Get sales metrics
            setSalesMetrics(connection, stats, startDate, endDate);
            
            // Get customer metrics
            setCustomerMetrics(connection, stats, startDate, endDate);
            
            // Get inventory metrics
            setInventoryMetrics(connection, stats);
            
            // Calculate growth metrics
            calculateGrowthMetrics(connection, stats, startDate, endDate, periodType);
            
        } catch (SQLException e) {
            e.printStackTrace();
            throw e;
        }
        
        return stats;
    }
    
    /**
     * Set financial metrics (revenue, profit, average order value)
     */
    private static void setFinancialMetrics(Connection connection, BusinessStatistics stats, 
                                          LocalDate startDate, LocalDate endDate) throws SQLException {
        String sql = """
            SELECT 
                SUM(o.total_price) as total_revenue,
                COUNT(DISTINCT o.order_id) as total_orders,
                AVG(o.total_price) as avg_order_value
            FROM Orders o 
            WHERE DATE(o.order_date) BETWEEN ? AND ? 
            AND o.is_deleted = false
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setDate(1, Date.valueOf(startDate));
            stmt.setDate(2, Date.valueOf(endDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    stats.setTotalRevenue(rs.getDouble("total_revenue"));
                    stats.setTotalOrders(rs.getInt("total_orders"));
                    stats.setAverageOrderValue(rs.getDouble("avg_order_value"));
                }
            }
        }
        
        // Calculate estimated profit (assuming 30% profit margin for simplification)
        // In a real system, you'd have cost data to calculate actual profit
        stats.setTotalProfit(stats.getTotalRevenue() * 0.30);
    }
    
    /**
     * Set sales metrics (items sold, pets vs products)
     */
    private static void setSalesMetrics(Connection connection, BusinessStatistics stats, 
                                      LocalDate startDate, LocalDate endDate) throws SQLException {
        String sql = """
            SELECT 
                SUM(od.quantity) as total_items_sold,
                SUM(CASE WHEN od.item_type = 'pet' THEN od.quantity ELSE 0 END) as pets_sold,
                SUM(CASE WHEN od.item_type = 'product' THEN od.quantity ELSE 0 END) as products_sold
            FROM Orders o 
            JOIN OrderDetails od ON o.order_id = od.order_id
            WHERE DATE(o.order_date) BETWEEN ? AND ? 
            AND o.is_deleted = false
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setDate(1, Date.valueOf(startDate));
            stmt.setDate(2, Date.valueOf(endDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    stats.setTotalItemsSold(rs.getInt("total_items_sold"));
                    stats.setPetsSold(rs.getInt("pets_sold"));
                    stats.setProductsSold(rs.getInt("products_sold"));
                }
            }
        }
    }
    
    /**
     * Set customer metrics (new vs returning customers)
     */
    private static void setCustomerMetrics(Connection connection, BusinessStatistics stats, 
                                         LocalDate startDate, LocalDate endDate) throws SQLException {
        // Total unique customers in period
        String totalCustomersSQL = """
            SELECT COUNT(DISTINCT o.customer_id) as total_customers
            FROM Orders o 
            WHERE DATE(o.order_date) BETWEEN ? AND ? 
            AND o.is_deleted = false
        """;
        
        // New customers (first order in this period)
        String newCustomersSQL = """
            SELECT COUNT(DISTINCT o.customer_id) as new_customers
            FROM Orders o 
            WHERE DATE(o.order_date) BETWEEN ? AND ? 
            AND o.is_deleted = false
            AND o.customer_id NOT IN (
                SELECT DISTINCT customer_id 
                FROM Orders 
                WHERE DATE(order_date) < ? 
                AND is_deleted = false
            )
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(totalCustomersSQL)) {
            stmt.setDate(1, Date.valueOf(startDate));
            stmt.setDate(2, Date.valueOf(endDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    stats.setTotalCustomers(rs.getInt("total_customers"));
                }
            }
        }
        
        try (PreparedStatement stmt = connection.prepareStatement(newCustomersSQL)) {
            stmt.setDate(1, Date.valueOf(startDate));
            stmt.setDate(2, Date.valueOf(endDate));
            stmt.setDate(3, Date.valueOf(startDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    stats.setNewCustomers(rs.getInt("new_customers"));
                    stats.setReturningCustomers(stats.getTotalCustomers() - stats.getNewCustomers());
                }
            }
        }
    }
    
    /**
     * Set inventory metrics (current stock levels)
     */
    private static void setInventoryMetrics(Connection connection, BusinessStatistics stats) throws SQLException {
        // Count available pets
        String petsSQL = "SELECT COUNT(*) as pet_count FROM Pets WHERE is_sold = false";
        
        // Count products in stock (assuming stock > 0 means available)
        String productsSQL = "SELECT SUM(stock) as product_count FROM Products WHERE stock > 0";
        
        // Count low stock products (stock <= 5 as threshold)
        String lowStockSQL = "SELECT COUNT(*) as low_stock_count FROM Products WHERE stock > 0 AND stock <= 5";
        
        try (PreparedStatement stmt = connection.prepareStatement(petsSQL);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                stats.setTotalPetsInStock(rs.getInt("pet_count"));
            }
        }
        
        try (PreparedStatement stmt = connection.prepareStatement(productsSQL);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                stats.setTotalProductsInStock(rs.getInt("product_count"));
            }
        }
        
        try (PreparedStatement stmt = connection.prepareStatement(lowStockSQL);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                stats.setLowStockProducts(rs.getInt("low_stock_count"));
            }
        }
    }
    
    /**
     * Calculate growth metrics compared to previous period
     */
    private static void calculateGrowthMetrics(Connection connection, BusinessStatistics stats, 
                                             LocalDate startDate, LocalDate endDate, String periodType) throws SQLException {
        // Calculate previous period dates
        LocalDate prevStartDate, prevEndDate;
        long daysDiff = java.time.temporal.ChronoUnit.DAYS.between(startDate, endDate) + 1;
        
        prevEndDate = startDate.minusDays(1);
        prevStartDate = prevEndDate.minusDays(daysDiff - 1);
        
        // Get previous period revenue
        String prevRevenueSQL = """
            SELECT SUM(o.total_price) as prev_revenue, COUNT(DISTINCT o.order_id) as prev_orders
            FROM Orders o 
            WHERE DATE(o.order_date) BETWEEN ? AND ? 
            AND o.is_deleted = false
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(prevRevenueSQL)) {
            stmt.setDate(1, Date.valueOf(prevStartDate));
            stmt.setDate(2, Date.valueOf(prevEndDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    double prevRevenue = rs.getDouble("prev_revenue");
                    int prevOrders = rs.getInt("prev_orders");
                    
                    // Calculate revenue growth percentage
                    if (prevRevenue > 0) {
                        double revenueGrowth = ((stats.getTotalRevenue() - prevRevenue) / prevRevenue) * 100;
                        stats.setRevenueGrowth(revenueGrowth);
                    }
                    
                    // Calculate orders growth
                    if (prevOrders > 0) {
                        int ordersGrowth = ((stats.getTotalOrders() - prevOrders) * 100) / prevOrders;
                        stats.setOrdersGrowth(ordersGrowth);
                    }
                }
            }
        }
    }
    
    /**
     * Get daily revenue data for charts
     */
    public static Map<String, Double> getDailyRevenue(LocalDate startDate, LocalDate endDate) throws SQLException {
        Map<String, Double> dailyRevenue = new HashMap<>();
        Connection connection = DatabaseUtil.getConnection();
        
        String sql = """
            SELECT DATE(o.order_date) as order_date, SUM(o.total_price) as daily_revenue
            FROM Orders o 
            WHERE DATE(o.order_date) BETWEEN ? AND ? 
            AND o.is_deleted = false
            GROUP BY DATE(o.order_date)
            ORDER BY DATE(o.order_date)
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setDate(1, Date.valueOf(startDate));
            stmt.setDate(2, Date.valueOf(endDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String date = rs.getDate("order_date").toString();
                    double revenue = rs.getDouble("daily_revenue");
                    dailyRevenue.put(date, revenue);
                }
            }
        }
        
        return dailyRevenue;
    }
    
    /**
     * Get monthly revenue data for charts
     */
    public static Map<String, Double> getMonthlyRevenue(LocalDate startDate, LocalDate endDate) throws SQLException {
        Map<String, Double> monthlyRevenue = new HashMap<>();
        Connection connection = DatabaseUtil.getConnection();
        
        String sql = """
            SELECT 
                YEAR(o.order_date) as year,
                MONTH(o.order_date) as month,
                SUM(o.total_price) as monthly_revenue
            FROM Orders o 
            WHERE DATE(o.order_date) BETWEEN ? AND ? 
            AND o.is_deleted = false
            GROUP BY YEAR(o.order_date), MONTH(o.order_date)
            ORDER BY YEAR(o.order_date), MONTH(o.order_date)
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setDate(1, Date.valueOf(startDate));
            stmt.setDate(2, Date.valueOf(endDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    int year = rs.getInt("year");
                    int month = rs.getInt("month");
                    double revenue = rs.getDouble("monthly_revenue");
                    String monthKey = String.format("%d-%02d", year, month);
                    monthlyRevenue.put(monthKey, revenue);
                }
            }
        }
        
        return monthlyRevenue;
    }
}
