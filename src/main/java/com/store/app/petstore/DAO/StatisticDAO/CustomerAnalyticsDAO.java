package com.store.app.petstore.DAO.StatisticDAO;

import com.store.app.petstore.DAO.DatabaseUtil;
import com.store.app.petstore.Models.Records.CustomerAnalytics;

import java.sql.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * DAO for customer analytics and behavior analysis
 */
public class CustomerAnalyticsDAO {
    
    /**
     * Get comprehensive customer analytics for all customers
     */
    public static List<CustomerAnalytics> getAllCustomerAnalytics() throws SQLException {
        List<CustomerAnalytics> customerList = new ArrayList<>();
        Connection connection = DatabaseUtil.getConnection();
        
        String sql = """
            SELECT 
                c.customer_id,
                c.full_name,
                c.phone,
                MIN(DATE(o.order_date)) as first_purchase_date,
                MAX(DATE(o.order_date)) as last_purchase_date,
                COUNT(DISTINCT o.order_id) as total_orders,
                SUM(o.total_price) as total_spent,
                SUM(CASE WHEN od.item_type = 'pet' THEN od.quantity ELSE 0 END) as pets_purchased,
                SUM(CASE WHEN od.item_type = 'product' THEN od.quantity ELSE 0 END) as products_purchased,
                SUM(CASE WHEN od.item_type = 'pet' THEN od.quantity * od.unit_price ELSE 0 END) as pet_spending,
                SUM(CASE WHEN od.item_type = 'product' THEN od.quantity * od.unit_price ELSE 0 END) as product_spending
            FROM Customers c
            LEFT JOIN Orders o ON c.customer_id = o.customer_id AND o.is_deleted = false
            LEFT JOIN OrderDetails od ON o.order_id = od.order_id
            GROUP BY c.customer_id, c.full_name, c.phone
            ORDER BY total_spent DESC
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                CustomerAnalytics analytics = new CustomerAnalytics(
                    rs.getInt("customer_id"),
                    rs.getString("full_name"),
                    rs.getString("phone")
                );
                
                // Set purchase dates
                Date firstPurchase = rs.getDate("first_purchase_date");
                Date lastPurchase = rs.getDate("last_purchase_date");
                
                if (firstPurchase != null) {
                    analytics.setFirstPurchaseDate(firstPurchase.toLocalDate());
                }
                if (lastPurchase != null) {
                    analytics.setLastPurchaseDate(lastPurchase.toLocalDate());
                }
                
                // Set purchase metrics
                analytics.setTotalOrders(rs.getInt("total_orders"));
                analytics.setTotalSpent(rs.getDouble("total_spent"));
                analytics.setPetsPurchased(rs.getInt("pets_purchased"));
                analytics.setProductsPurchased(rs.getInt("products_purchased"));
                analytics.setPetSpending(rs.getDouble("pet_spending"));
                analytics.setProductSpending(rs.getDouble("product_spending"));
                
                // Determine favorite category
                analytics.setFavoriteCategory(determineFavoriteCategory(connection, analytics.getCustomerId()));
                
                customerList.add(analytics);
            }
        }
        
        return customerList;
    }
    
    /**
     * Get customer analytics for a specific customer
     */
    public static CustomerAnalytics getCustomerAnalytics(int customerId) throws SQLException {
        List<CustomerAnalytics> allCustomers = getAllCustomerAnalytics();
        
        for (CustomerAnalytics customer : allCustomers) {
            if (customer.getCustomerId() == customerId) {
                return customer;
            }
        }
        
        return null;
    }
    
    /**
     * Get customers by segment
     */
    public static List<CustomerAnalytics> getCustomersBySegment(String segment) throws SQLException {
        List<CustomerAnalytics> allCustomers = getAllCustomerAnalytics();
        List<CustomerAnalytics> segmentCustomers = new ArrayList<>();
        
        for (CustomerAnalytics customer : allCustomers) {
            if (segment.equals(customer.getCustomerSegment())) {
                segmentCustomers.add(customer);
            }
        }
        
        return segmentCustomers;
    }
    
    /**
     * Get VIP customers (high value customers)
     */
    public static List<CustomerAnalytics> getVIPCustomers() throws SQLException {
        return getCustomersBySegment("vip");
    }
    
    /**
     * Get at-risk customers (haven't purchased recently)
     */
    public static List<CustomerAnalytics> getAtRiskCustomers() throws SQLException {
        return getCustomersBySegment("at_risk");
    }
    
    /**
     * Get lost customers (haven't purchased in a long time)
     */
    public static List<CustomerAnalytics> getLostCustomers() throws SQLException {
        return getCustomersBySegment("lost");
    }
    
    /**
     * Get new customers (first purchase recently)
     */
    public static List<CustomerAnalytics> getNewCustomers() throws SQLException {
        return getCustomersBySegment("new");
    }
    
    /**
     * Get top customers by spending
     */
    public static List<CustomerAnalytics> getTopCustomersBySpending(int limit) throws SQLException {
        List<CustomerAnalytics> allCustomers = getAllCustomerAnalytics();
        
        // Sort by total spent (descending)
        allCustomers.sort((a, b) -> Double.compare(b.getTotalSpent(), a.getTotalSpent()));
        
        // Return top customers
        return allCustomers.subList(0, Math.min(limit, allCustomers.size()));
    }
    
    /**
     * Get customer acquisition trends
     */
    public static Map<String, Integer> getCustomerAcquisitionTrends(LocalDate startDate, LocalDate endDate) throws SQLException {
        Map<String, Integer> trends = new HashMap<>();
        Connection connection = DatabaseUtil.getConnection();
        
        String sql = """
            SELECT 
                DATE(MIN(o.order_date)) as acquisition_date,
                COUNT(DISTINCT o.customer_id) as new_customers
            FROM Orders o
            WHERE DATE(o.order_date) BETWEEN ? AND ?
            AND o.is_deleted = false
            AND o.customer_id NOT IN (
                SELECT DISTINCT customer_id 
                FROM Orders 
                WHERE DATE(order_date) < ? 
                AND is_deleted = false
            )
            GROUP BY DATE(MIN(o.order_date))
            ORDER BY acquisition_date
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setDate(1, Date.valueOf(startDate));
            stmt.setDate(2, Date.valueOf(endDate));
            stmt.setDate(3, Date.valueOf(startDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String date = rs.getDate("acquisition_date").toString();
                    int newCustomers = rs.getInt("new_customers");
                    trends.put(date, newCustomers);
                }
            }
        }
        
        return trends;
    }
    
    /**
     * Get customer retention rate for a specific period
     */
    public static double getCustomerRetentionRate(LocalDate startDate, LocalDate endDate) throws SQLException {
        Connection connection = DatabaseUtil.getConnection();
        
        // Get customers who made purchases in the previous period
        LocalDate prevStartDate = startDate.minusMonths(1);
        LocalDate prevEndDate = startDate.minusDays(1);
        
        String sql = """
            SELECT 
                COUNT(DISTINCT prev_customers.customer_id) as prev_period_customers,
                COUNT(DISTINCT current_customers.customer_id) as retained_customers
            FROM (
                SELECT DISTINCT customer_id 
                FROM Orders 
                WHERE DATE(order_date) BETWEEN ? AND ? 
                AND is_deleted = false
            ) prev_customers
            LEFT JOIN (
                SELECT DISTINCT customer_id 
                FROM Orders 
                WHERE DATE(order_date) BETWEEN ? AND ? 
                AND is_deleted = false
            ) current_customers ON prev_customers.customer_id = current_customers.customer_id
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setDate(1, Date.valueOf(prevStartDate));
            stmt.setDate(2, Date.valueOf(prevEndDate));
            stmt.setDate(3, Date.valueOf(startDate));
            stmt.setDate(4, Date.valueOf(endDate));
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    int prevCustomers = rs.getInt("prev_period_customers");
                    int retainedCustomers = rs.getInt("retained_customers");
                    
                    if (prevCustomers > 0) {
                        return ((double) retainedCustomers / prevCustomers) * 100;
                    }
                }
            }
        }
        
        return 0.0;
    }
    
    /**
     * Get average customer lifetime value
     */
    public static double getAverageCustomerLifetimeValue() throws SQLException {
        List<CustomerAnalytics> allCustomers = getAllCustomerAnalytics();
        
        if (allCustomers.isEmpty()) {
            return 0.0;
        }
        
        double totalCLV = 0;
        for (CustomerAnalytics customer : allCustomers) {
            totalCLV += customer.getCustomerLifetimeValue();
        }
        
        return totalCLV / allCustomers.size();
    }
    
    /**
     * Get customer segment distribution
     */
    public static Map<String, Integer> getCustomerSegmentDistribution() throws SQLException {
        Map<String, Integer> distribution = new HashMap<>();
        List<CustomerAnalytics> allCustomers = getAllCustomerAnalytics();
        
        // Initialize all segments
        distribution.put("new", 0);
        distribution.put("regular", 0);
        distribution.put("vip", 0);
        distribution.put("at_risk", 0);
        distribution.put("lost", 0);
        
        // Count customers in each segment
        for (CustomerAnalytics customer : allCustomers) {
            String segment = customer.getCustomerSegment();
            distribution.put(segment, distribution.getOrDefault(segment, 0) + 1);
        }
        
        return distribution;
    }
    
    /**
     * Helper method to determine customer's favorite category
     */
    private static String determineFavoriteCategory(Connection connection, int customerId) throws SQLException {
        String sql = """
            SELECT 
                CASE 
                    WHEN od.item_type = 'pet' THEN p.type
                    WHEN od.item_type = 'product' THEN pr.category
                END as category,
                SUM(od.quantity * od.unit_price) as spending
            FROM Orders o
            JOIN OrderDetails od ON o.order_id = od.order_id
            LEFT JOIN Pets p ON od.item_type = 'pet' AND od.item_id = p.pet_id
            LEFT JOIN Products pr ON od.item_type = 'product' AND od.item_id = pr.product_id
            WHERE o.customer_id = ? AND o.is_deleted = false
            GROUP BY category
            ORDER BY spending DESC
            LIMIT 1
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setInt(1, customerId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("category");
                }
            }
        }
        
        return "Unknown";
    }
    
    /**
     * Get customers who haven't purchased in X days
     */
    public static List<CustomerAnalytics> getInactiveCustomers(int daysSinceLastPurchase) throws SQLException {
        List<CustomerAnalytics> allCustomers = getAllCustomerAnalytics();
        List<CustomerAnalytics> inactiveCustomers = new ArrayList<>();
        
        for (CustomerAnalytics customer : allCustomers) {
            if (customer.getDaysSinceLastPurchase() >= daysSinceLastPurchase) {
                inactiveCustomers.add(customer);
            }
        }
        
        return inactiveCustomers;
    }
    
    /**
     * Get repeat purchase rate
     */
    public static double getRepeatPurchaseRate() throws SQLException {
        List<CustomerAnalytics> allCustomers = getAllCustomerAnalytics();
        
        if (allCustomers.isEmpty()) {
            return 0.0;
        }
        
        int repeatCustomers = 0;
        for (CustomerAnalytics customer : allCustomers) {
            if (customer.isReturningCustomer()) {
                repeatCustomers++;
            }
        }
        
        return ((double) repeatCustomers / allCustomers.size()) * 100;
    }
}
