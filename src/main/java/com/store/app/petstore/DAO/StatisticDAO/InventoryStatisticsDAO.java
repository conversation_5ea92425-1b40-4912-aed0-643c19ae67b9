package com.store.app.petstore.DAO.StatisticDAO;

import com.store.app.petstore.DAO.DatabaseUtil;
import com.store.app.petstore.Models.Records.InventoryStatistics;

import java.sql.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * DAO for inventory statistics and stock management analytics
 */
public class InventoryStatisticsDAO {
    
    /**
     * Get comprehensive inventory statistics for all products
     */
    public static List<InventoryStatistics> getAllInventoryStatistics() throws SQLException {
        List<InventoryStatistics> inventoryList = new ArrayList<>();
        Connection connection = DatabaseUtil.getConnection();
        
        // Get product inventory statistics
        inventoryList.addAll(getProductInventoryStatistics(connection));
        
        // Get pet inventory statistics
        inventoryList.addAll(getPetInventoryStatistics(connection));
        
        return inventoryList;
    }
    
    /**
     * Get inventory statistics for products only
     */
    public static List<InventoryStatistics> getProductInventoryStatistics() throws SQLException {
        Connection connection = DatabaseUtil.getConnection();
        return getProductInventoryStatistics(connection);
    }
    
    /**
     * Get inventory statistics for pets only
     */
    public static List<InventoryStatistics> getPetInventoryStatistics() throws SQLException {
        Connection connection = DatabaseUtil.getConnection();
        return getPetInventoryStatistics(connection);
    }
    
    /**
     * Internal method to get product inventory statistics
     */
    private static List<InventoryStatistics> getProductInventoryStatistics(Connection connection) throws SQLException {
        List<InventoryStatistics> productStats = new ArrayList<>();
        
        String sql = """
            SELECT 
                p.name,
                p.category,
                p.stock as current_stock,
                p.price as unit_price,
                COALESCE(sales.sold_this_month, 0) as sold_this_month
            FROM Products p
            LEFT JOIN (
                SELECT 
                    od.item_id,
                    SUM(od.quantity) as sold_this_month
                FROM Orders o
                JOIN OrderDetails od ON o.order_id = od.order_id
                WHERE od.item_type = 'product'
                AND DATE(o.order_date) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                AND o.is_deleted = false
                GROUP BY od.item_id
            ) sales ON p.product_id = sales.item_id
            ORDER BY p.name
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String name = rs.getString("name");
                String category = rs.getString("category");
                int currentStock = rs.getInt("current_stock");
                double unitPrice = rs.getDouble("unit_price");
                int soldThisMonth = rs.getInt("sold_this_month");
                
                // Set minimum stock threshold based on category (business logic)
                int minimumStock = getMinimumStockThreshold(category);
                
                InventoryStatistics stats = new InventoryStatistics(
                    name, "product", category, currentStock, minimumStock, soldThisMonth, unitPrice
                );
                
                productStats.add(stats);
            }
        }
        
        return productStats;
    }
    
    /**
     * Internal method to get pet inventory statistics
     */
    private static List<InventoryStatistics> getPetInventoryStatistics(Connection connection) throws SQLException {
        List<InventoryStatistics> petStats = new ArrayList<>();
        
        String sql = """
            SELECT 
                p.type as category,
                COUNT(CASE WHEN p.is_sold = false THEN 1 END) as current_stock,
                AVG(p.price) as avg_price,
                COALESCE(sales.sold_this_month, 0) as sold_this_month
            FROM Pets p
            LEFT JOIN (
                SELECT 
                    pet_type.type,
                    COUNT(*) as sold_this_month
                FROM Orders o
                JOIN OrderDetails od ON o.order_id = od.order_id
                JOIN Pets pet_type ON od.item_id = pet_type.pet_id
                WHERE od.item_type = 'pet'
                AND DATE(o.order_date) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                AND o.is_deleted = false
                GROUP BY pet_type.type
            ) sales ON p.type = sales.type
            GROUP BY p.type
            ORDER BY p.type
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                String category = rs.getString("category");
                int currentStock = rs.getInt("current_stock");
                double avgPrice = rs.getDouble("avg_price");
                int soldThisMonth = rs.getInt("sold_this_month");
                
                // For pets, minimum stock is typically lower
                int minimumStock = 2; // Keep at least 2 pets of each type
                
                InventoryStatistics stats = new InventoryStatistics(
                    category + " Pets", "pet", category, currentStock, minimumStock, soldThisMonth, avgPrice
                );
                
                petStats.add(stats);
            }
        }
        
        return petStats;
    }
    
    /**
     * Get low stock items that need attention
     */
    public static List<InventoryStatistics> getLowStockItems() throws SQLException {
        List<InventoryStatistics> allItems = getAllInventoryStatistics();
        List<InventoryStatistics> lowStockItems = new ArrayList<>();
        
        for (InventoryStatistics item : allItems) {
            if (item.isLowStock()) {
                lowStockItems.add(item);
            }
        }
        
        return lowStockItems;
    }
    
    /**
     * Get out of stock items
     */
    public static List<InventoryStatistics> getOutOfStockItems() throws SQLException {
        List<InventoryStatistics> allItems = getAllInventoryStatistics();
        List<InventoryStatistics> outOfStockItems = new ArrayList<>();
        
        for (InventoryStatistics item : allItems) {
            if (item.isOutOfStock()) {
                outOfStockItems.add(item);
            }
        }
        
        return outOfStockItems;
    }
    
    /**
     * Get overstocked items
     */
    public static List<InventoryStatistics> getOverstockedItems() throws SQLException {
        List<InventoryStatistics> allItems = getAllInventoryStatistics();
        List<InventoryStatistics> overstockedItems = new ArrayList<>();
        
        for (InventoryStatistics item : allItems) {
            if (item.isOverstocked()) {
                overstockedItems.add(item);
            }
        }
        
        return overstockedItems;
    }
    
    /**
     * Get inventory value summary
     */
    public static double getTotalInventoryValue() throws SQLException {
        Connection connection = DatabaseUtil.getConnection();
        double totalValue = 0;
        
        // Calculate product inventory value
        String productSQL = "SELECT SUM(stock * price) as product_value FROM Products WHERE stock > 0";
        try (PreparedStatement stmt = connection.prepareStatement(productSQL);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                totalValue += rs.getDouble("product_value");
            }
        }
        
        // Calculate pet inventory value
        String petSQL = "SELECT SUM(price) as pet_value FROM Pets WHERE is_sold = false";
        try (PreparedStatement stmt = connection.prepareStatement(petSQL);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                totalValue += rs.getDouble("pet_value");
            }
        }
        
        return totalValue;
    }
    
    /**
     * Get fast-moving items (high turnover rate)
     */
    public static List<InventoryStatistics> getFastMovingItems(int limit) throws SQLException {
        List<InventoryStatistics> allItems = getAllInventoryStatistics();
        
        // Sort by turnover rate (descending)
        allItems.sort((a, b) -> Double.compare(b.getTurnoverRate(), a.getTurnoverRate()));
        
        // Return top items
        return allItems.subList(0, Math.min(limit, allItems.size()));
    }
    
    /**
     * Get slow-moving items (low turnover rate)
     */
    public static List<InventoryStatistics> getSlowMovingItems(int limit) throws SQLException {
        List<InventoryStatistics> allItems = getAllInventoryStatistics();
        
        // Filter items with stock > 0 and sort by turnover rate (ascending)
        allItems.removeIf(item -> item.getCurrentStock() == 0);
        allItems.sort((a, b) -> Double.compare(a.getTurnoverRate(), b.getTurnoverRate()));
        
        // Return bottom items
        return allItems.subList(0, Math.min(limit, allItems.size()));
    }
    
    /**
     * Get inventory statistics by category
     */
    public static List<InventoryStatistics> getInventoryByCategory(String category) throws SQLException {
        List<InventoryStatistics> allItems = getAllInventoryStatistics();
        List<InventoryStatistics> categoryItems = new ArrayList<>();
        
        for (InventoryStatistics item : allItems) {
            if (item.getCategory().equalsIgnoreCase(category)) {
                categoryItems.add(item);
            }
        }
        
        return categoryItems;
    }
    
    /**
     * Helper method to determine minimum stock threshold based on category
     */
    private static int getMinimumStockThreshold(String category) {
        // Business logic for minimum stock levels
        switch (category.toLowerCase()) {
            case "food":
            case "treats":
                return 20; // Food items should have higher minimum stock
            case "toys":
                return 10;
            case "accessories":
                return 15;
            case "health":
            case "medicine":
                return 25; // Health items are critical
            default:
                return 10; // Default minimum stock
        }
    }
    
    /**
     * Get inventory turnover rate for the entire store
     */
    public static double getOverallInventoryTurnover() throws SQLException {
        Connection connection = DatabaseUtil.getConnection();
        
        String sql = """
            SELECT 
                SUM(od.quantity * od.unit_price) as total_sales,
                (SELECT SUM(stock * price) FROM Products WHERE stock > 0) + 
                (SELECT SUM(price) FROM Pets WHERE is_sold = false) as avg_inventory
            FROM Orders o
            JOIN OrderDetails od ON o.order_id = od.order_id
            WHERE DATE(o.order_date) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            AND o.is_deleted = false
        """;
        
        try (PreparedStatement stmt = connection.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                double totalSales = rs.getDouble("total_sales");
                double avgInventory = rs.getDouble("avg_inventory");
                
                if (avgInventory > 0) {
                    return totalSales / avgInventory;
                }
            }
        }
        
        return 0.0;
    }
}
