<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.chart.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.store.app.petstore.Controllers.Admin.Statistic.ComprehensiveDashboardController">
   <top>
      <VBox spacing="10.0" style="-fx-background-color: #f8f9fa; -fx-padding: 20;">
         <children>
            <!-- Header -->
            <HBox alignment="CENTER_LEFT" spacing="20.0">
               <children>
                  <Label style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: #2c3e50;" text="Pet Shop Business Dashboard" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Label fx:id="statusLabel" style="-fx-font-size: 12px; -fx-text-fill: #6c757d;" text="Ready" />
                  <ProgressBar fx:id="loadingProgressBar" prefWidth="100.0" visible="false" />
               </children>
            </HBox>
            
            <!-- Date Controls -->
            <HBox alignment="CENTER_LEFT" spacing="15.0">
               <children>
                  <Label style="-fx-font-weight: bold;" text="Period:" />
                  <DatePicker fx:id="startDatePicker" promptText="Start Date" />
                  <Label text="to" />
                  <DatePicker fx:id="endDatePicker" promptText="End Date" />
                  <ChoiceBox fx:id="periodChoiceBox" prefWidth="100.0" />
                  <Button fx:id="refreshButton" style="-fx-background-color: #007bff; -fx-text-fill: white;" text="Refresh" />
                  <Button onAction="#exportDashboard" style="-fx-background-color: #28a745; -fx-text-fill: white;" text="Export" />
               </children>
            </HBox>
         </children>
      </VBox>
   </top>
   
   <center>
      <TabPane fx:id="mainTabPane" tabClosingPolicy="UNAVAILABLE">
         
         <!-- Overview Tab -->
         <Tab fx:id="overviewTab" text="Overview">
            <content>
               <ScrollPane fitToWidth="true">
                  <content>
                     <VBox spacing="20.0" style="-fx-padding: 20;">
                        <children>
                           <!-- Quick Statistics Cards -->
                           <GridPane hgap="15.0" vgap="15.0">
                              <columnConstraints>
                                 <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                 <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                 <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                                 <ColumnConstraints hgrow="SOMETIMES" minWidth="10.0" prefWidth="100.0" />
                              </columnConstraints>
                              <rowConstraints>
                                 <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                                 <RowConstraints minHeight="10.0" prefHeight="30.0" vgrow="SOMETIMES" />
                              </rowConstraints>
                              <children>
                                 <!-- Revenue Card -->
                                 <VBox alignment="CENTER" style="-fx-background-color: #007bff; -fx-background-radius: 10; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);">
                                    <children>
                                       <Label style="-fx-font-size: 14px; -fx-text-fill: white; -fx-opacity: 0.8;" text="Total Revenue" />
                                       <Label fx:id="totalRevenueLabel" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="$0.00" />
                                    </children>
                                 </VBox>
                                 
                                 <!-- Profit Card -->
                                 <VBox alignment="CENTER" style="-fx-background-color: #28a745; -fx-background-radius: 10; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="1">
                                    <children>
                                       <Label style="-fx-font-size: 14px; -fx-text-fill: white; -fx-opacity: 0.8;" text="Total Profit" />
                                       <Label fx:id="totalProfitLabel" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="$0.00" />
                                    </children>
                                 </VBox>
                                 
                                 <!-- Orders Card -->
                                 <VBox alignment="CENTER" style="-fx-background-color: #ffc107; -fx-background-radius: 10; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="2">
                                    <children>
                                       <Label style="-fx-font-size: 14px; -fx-text-fill: white; -fx-opacity: 0.8;" text="Total Orders" />
                                       <Label fx:id="totalOrdersLabel" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="0" />
                                    </children>
                                 </VBox>
                                 
                                 <!-- Average Order Value Card -->
                                 <VBox alignment="CENTER" style="-fx-background-color: #17a2b8; -fx-background-radius: 10; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="3">
                                    <children>
                                       <Label style="-fx-font-size: 14px; -fx-text-fill: white; -fx-opacity: 0.8;" text="Avg Order Value" />
                                       <Label fx:id="averageOrderValueLabel" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="$0.00" />
                                    </children>
                                 </VBox>
                                 
                                 <!-- Revenue Growth Card -->
                                 <VBox alignment="CENTER" style="-fx-background-color: #6f42c1; -fx-background-radius: 10; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.rowIndex="1">
                                    <children>
                                       <Label style="-fx-font-size: 14px; -fx-text-fill: white; -fx-opacity: 0.8;" text="Revenue Growth" />
                                       <Label fx:id="revenueGrowthLabel" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="0%" />
                                    </children>
                                 </VBox>
                                 
                                 <!-- Customer Count Card -->
                                 <VBox alignment="CENTER" style="-fx-background-color: #e83e8c; -fx-background-radius: 10; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="1" GridPane.rowIndex="1">
                                    <children>
                                       <Label style="-fx-font-size: 14px; -fx-text-fill: white; -fx-opacity: 0.8;" text="Total Customers" />
                                       <Label fx:id="customerCountLabel" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="0" />
                                    </children>
                                 </VBox>
                                 
                                 <!-- New Customers Card -->
                                 <VBox alignment="CENTER" style="-fx-background-color: #fd7e14; -fx-background-radius: 10; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="2" GridPane.rowIndex="1">
                                    <children>
                                       <Label style="-fx-font-size: 14px; -fx-text-fill: white; -fx-opacity: 0.8;" text="New Customers" />
                                       <Label fx:id="newCustomersLabel" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="0" />
                                    </children>
                                 </VBox>
                                 
                                 <!-- Inventory Value Card -->
                                 <VBox alignment="CENTER" style="-fx-background-color: #20c997; -fx-background-radius: 10; -fx-padding: 20; -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 5, 0, 0, 2);" GridPane.columnIndex="3" GridPane.rowIndex="1">
                                    <children>
                                       <Label style="-fx-font-size: 14px; -fx-text-fill: white; -fx-opacity: 0.8;" text="Inventory Value" />
                                       <Label fx:id="inventoryValueLabel" style="-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;" text="$0.00" />
                                    </children>
                                 </VBox>
                              </children>
                           </GridPane>
                           
                           <!-- Charts Section -->
                           <HBox spacing="20.0">
                              <children>
                                 <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                                    <children>
                                       <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Revenue Trend" />
                                       <LineChart fx:id="revenueChart" prefHeight="300.0">
                                          <xAxis>
                                             <CategoryAxis side="BOTTOM" />
                                          </xAxis>
                                          <yAxis>
                                             <NumberAxis side="LEFT" />
                                          </yAxis>
                                       </LineChart>
                                    </children>
                                 </VBox>
                                 
                                 <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                                    <children>
                                       <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Sales Distribution" />
                                       <PieChart fx:id="salesDistributionChart" prefHeight="300.0" />
                                    </children>
                                 </VBox>
                              </children>
                           </HBox>
                        </children>
                     </VBox>
                  </content>
               </ScrollPane>
            </content>
         </Tab>
         
         <!-- Sales Tab -->
         <Tab fx:id="salesTab" text="Sales Analytics">
            <content>
               <VBox spacing="20.0" style="-fx-padding: 20;">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold;" text="Sales Performance" />
                     
                     <!-- Customer Segment Chart -->
                     <VBox spacing="10.0">
                        <children>
                           <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Customer Segments" />
                           <BarChart fx:id="customerSegmentChart" prefHeight="300.0">
                              <xAxis>
                                 <CategoryAxis side="BOTTOM" />
                              </xAxis>
                              <yAxis>
                                 <NumberAxis side="LEFT" />
                              </yAxis>
                           </BarChart>
                        </children>
                     </VBox>
                     
                     <!-- Top Customers Table -->
                     <VBox spacing="10.0">
                        <children>
                           <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Top Customers" />
                           <TableView fx:id="topCustomersTable" prefHeight="200.0" />
                        </children>
                     </VBox>
                  </children>
               </VBox>
            </content>
         </Tab>
         
         <!-- Customers Tab -->
         <Tab fx:id="customersTab" text="Customer Analytics">
            <content>
               <VBox spacing="20.0" style="-fx-padding: 20;">
                  <children>
                     <Label style="-fx-font-size: 18px; -fx-font-weight: bold;" text="Customer Insights" />
                     <!-- Customer analytics content will be added here -->
                  </children>
               </VBox>
            </content>
         </Tab>
         
         <!-- Inventory Tab -->
         <Tab fx:id="inventoryTab" text="Inventory Management">
            <content>
               <VBox spacing="20.0" style="-fx-padding: 20;">
                  <children>
                     <HBox alignment="CENTER_LEFT" spacing="20.0">
                        <children>
                           <Label style="-fx-font-size: 18px; -fx-font-weight: bold;" text="Inventory Status" />
                           <Region HBox.hgrow="ALWAYS" />
                           <Label style="-fx-font-size: 14px; -fx-font-weight: bold;" text="Low Stock Alerts:" />
                           <Label fx:id="lowStockAlertsLabel" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #dc3545;" text="0" />
                        </children>
                     </HBox>
                     
                     <!-- Inventory Tables -->
                     <HBox spacing="20.0">
                        <children>
                           <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                              <children>
                                 <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Low Stock Items" />
                                 <TableView fx:id="lowStockTable" prefHeight="250.0" />
                              </children>
                           </VBox>
                           
                           <VBox spacing="10.0" HBox.hgrow="ALWAYS">
                              <children>
                                 <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Fast Moving Items" />
                                 <TableView fx:id="fastMovingItemsTable" prefHeight="250.0" />
                              </children>
                           </VBox>
                        </children>
                     </HBox>
                     
                     <!-- Inventory Trend Chart -->
                     <VBox spacing="10.0">
                        <children>
                           <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Inventory Trends" />
                           <AreaChart fx:id="inventoryTrendChart" prefHeight="300.0">
                              <xAxis>
                                 <CategoryAxis side="BOTTOM" />
                              </xAxis>
                              <yAxis>
                                 <NumberAxis side="LEFT" />
                              </yAxis>
                           </AreaChart>
                        </children>
                     </VBox>
                  </children>
               </VBox>
            </content>
         </Tab>
      </TabPane>
   </center>
</BorderPane>
